import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

// Mock mode for development when backend is not available
const bool kMockMode = true;

class AuthProvider extends ChangeNotifier {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  final AuthService _authService = AuthService();
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  User? _user;
  String? _token;
  bool _isLoading = false;
  bool _isAuthenticated = false;

  User? get user => _user;
  String? get token => _token;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  bool get isAdmin => _user?.isAdmin ?? false;

  AuthProvider() {
    _loadAuthData();
  }

  Future<void> _loadAuthData() async {
    try {
      _token = await _storage.read(key: _tokenKey);
      final userData = await _storage.read(key: _userKey);

      if (_token != null && userData != null) {
        _user = User.fromJson(userData);
        _isAuthenticated = true;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading auth data: $e');
    }
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);

    try {
      final response = await _authService.login(email, password);

      if (response['success'] == true) {
        _token = response['token'];
        _user = User.fromMap(response['user']);
        _isAuthenticated = true;

        await _saveAuthData();
        _setLoading(false);
        return true;
      } else {
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setLoading(false);
      debugPrint('Login error: $e');
      return false;
    }
  }

  Future<bool> register(
    String firstName,
    String lastName,
    String email,
    String password,
  ) async {
    _setLoading(true);

    try {
      final response = await _authService.register(
        firstName,
        lastName,
        email,
        password,
      );

      if (response['success'] == true) {
        _token = response['token'];
        _user = User.fromMap(response['user']);
        _isAuthenticated = true;

        await _saveAuthData();
        _setLoading(false);
        return true;
      } else {
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setLoading(false);
      debugPrint('Registration error: $e');
      return false;
    }
  }

  Future<bool> loginWithGoogle() async {
    _setLoading(true);

    try {
      final response = await _authService.loginWithGoogle();

      if (response['success'] == true) {
        _token = response['token'];
        _user = User.fromMap(response['user']);
        _isAuthenticated = true;

        await _saveAuthData();
        _setLoading(false);
        return true;
      } else {
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setLoading(false);
      debugPrint('Google login error: $e');
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);

    try {
      await _authService.logout();
    } catch (e) {
      debugPrint('Logout error: $e');
    }

    _user = null;
    _token = null;
    _isAuthenticated = false;

    await _clearAuthData();
    _setLoading(false);
  }

  Future<void> _saveAuthData() async {
    try {
      if (_token != null) {
        await _storage.write(key: _tokenKey, value: _token);
      }
      if (_user != null) {
        await _storage.write(key: _userKey, value: _user!.toJson());
      }
    } catch (e) {
      debugPrint('Error saving auth data: $e');
    }
  }

  Future<void> _clearAuthData() async {
    try {
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _userKey);
    } catch (e) {
      debugPrint('Error clearing auth data: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  Future<void> updateUser(User updatedUser) async {
    _user = updatedUser;
    await _saveAuthData();
    notifyListeners();
  }
}
