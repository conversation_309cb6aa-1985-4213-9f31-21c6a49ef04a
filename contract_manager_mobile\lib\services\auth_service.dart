import 'package:dio/dio.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'api_service.dart';

class AuthService {
  final ApiService _apiService = ApiService();
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    // Make Google Sign-In optional for development
    scopes: ['email', 'profile'],
  );

  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await _apiService.post('/login', {
        'email': email,
        'password': password,
      });

      if (response.statusCode == 200) {
        final data = response.data;
        return {'success': true, 'token': data['token'], 'user': data['user']};
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Login failed',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  Future<Map<String, dynamic>> register(
    String firstName,
    String lastName,
    String email,
    String password,
  ) async {
    try {
      final response = await _apiService.post('/register', {
        'firstname': firstName,
        'lastname': lastName,
        'email': email,
        'password': password,
        'password_confirmation': password,
      });

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = response.data;
        return {'success': true, 'token': data['token'], 'user': data['user']};
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Registration failed',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  Future<Map<String, dynamic>> loginWithGoogle() async {
    try {
      // Check if Google Sign-In is properly configured
      if (!await _googleSignIn.isSignedIn()) {
        // Sign out first to ensure account picker is shown
        await _googleSignIn.signOut();
      }

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        return {'success': false, 'message': 'Google sign-in was cancelled'};
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final response = await _apiService.post('/auth/google/callback', {
        'access_token': googleAuth.accessToken,
        'id_token': googleAuth.idToken,
      });

      if (response.statusCode == 200) {
        final data = response.data;
        return {'success': true, 'token': data['token'], 'user': data['user']};
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Google login failed',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      // Handle Google Sign-In configuration errors gracefully
      if (e.toString().contains('ClientID not set') ||
          e.toString().contains('CLIENT_ID')) {
        return {
          'success': false,
          'message':
              'Google Sign-In is not configured for this environment. Please use email/password login.',
        };
      }
      return {
        'success': false,
        'message': 'Google sign-in failed: ${e.toString()}',
      };
    }
  }

  Future<Map<String, dynamic>> forgotPassword(String email) async {
    try {
      final response = await _apiService.post('/forgot-password', {
        'email': email,
      });

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Password reset email sent',
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Failed to send reset email',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  Future<Map<String, dynamic>> resetPassword(
    String token,
    String email,
    String password,
  ) async {
    try {
      final response = await _apiService.post('/reset-password', {
        'token': token,
        'email': email,
        'password': password,
        'password_confirmation': password,
      });

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Password reset successful',
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Password reset failed',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  Future<void> logout() async {
    try {
      await _apiService.post('/logout', {});
      await _googleSignIn.signOut();
    } catch (e) {
      // Even if logout fails on server, we should clear local data
      await _googleSignIn.signOut();
    }
  }

  Future<Map<String, dynamic>> getProfile() async {
    try {
      final response = await _apiService.get('/profile');

      if (response.statusCode == 200) {
        return {'success': true, 'user': response.data['user']};
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Failed to get profile',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }
}
